package com.ruoyi.system.service.impl;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Random;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.LotteryActivity;
import com.ruoyi.system.domain.LotteryRecord;
import com.ruoyi.system.domain.Merchant;
import com.ruoyi.system.mapper.LotteryActivityMapper;
import com.ruoyi.system.mapper.LotteryRecordMapper;
import com.ruoyi.system.service.ILotteryActivityService;
import com.ruoyi.system.service.ILotteryRecordService;
import com.ruoyi.system.service.IMerchantService;

/**
 * 抽奖记录管理 服务层实现
 * 
 * <AUTHOR>
 */
@Service
public class LotteryRecordServiceImpl implements ILotteryRecordService
{
    @Autowired
    private LotteryRecordMapper lotteryRecordMapper;

    @Autowired
    private LotteryActivityMapper lotteryActivityMapper;

    @Autowired
    private ILotteryActivityService lotteryActivityService;

    @Autowired
    private IMerchantService merchantService;

    /**
     * 查询抽奖记录信息
     * 
     * @param recordId 记录ID
     * @return 记录信息
     */
    @Override
    @DataSource(DataSourceType.MASTER)
    public LotteryRecord selectLotteryRecordById(Long recordId)
    {
        return lotteryRecordMapper.selectLotteryRecordById(recordId);
    }

    /**
     * 查询抽奖记录列表
     * 
     * @param lotteryRecord 记录信息
     * @return 记录集合
     */
    @Override
    @DataSource(DataSourceType.SLAVE)
    public List<LotteryRecord> selectLotteryRecordList(LotteryRecord lotteryRecord)
    {
        return lotteryRecordMapper.selectLotteryRecordList(lotteryRecord);
    }

    /**
     * 根据活动ID查询抽奖记录列表
     * 
     * @param activityId 活动ID
     * @return 记录集合
     */
    @Override
    public List<LotteryRecord> selectLotteryRecordsByActivityId(Long activityId)
    {
        return lotteryRecordMapper.selectLotteryRecordsByActivityId(activityId);
    }

    /**
     * 根据商家ID查询抽奖记录列表
     * 
     * @param merchantId 商家ID
     * @return 记录集合
     */
    @Override
    public List<LotteryRecord> selectLotteryRecordsByMerchantId(Long merchantId)
    {
        return lotteryRecordMapper.selectLotteryRecordsByMerchantId(merchantId);
    }

    /**
     * 根据用户OpenID查询抽奖记录列表
     * 
     * @param userOpenid 用户OpenID
     * @return 记录集合
     */
    @Override
    public List<LotteryRecord> selectLotteryRecordsByUserOpenid(String userOpenid)
    {
        return lotteryRecordMapper.selectLotteryRecordsByUserOpenid(userOpenid);
    }

    /**
     * 查询用户在指定活动的抽奖记录
     * 
     * @param activityId 活动ID
     * @param userOpenid 用户OpenID
     * @return 记录集合
     */
    @Override
    public List<LotteryRecord> selectUserActivityRecords(Long activityId, String userOpenid)
    {
        return lotteryRecordMapper.selectUserActivityRecords(activityId, userOpenid);
    }

    /**
     * 查询用户今日在指定活动的抽奖次数
     * 
     * @param activityId 活动ID
     * @param userOpenid 用户OpenID
     * @return 抽奖次数
     */
    @Override
    public int countUserTodayDraws(Long activityId, String userOpenid)
    {
        return lotteryRecordMapper.countUserTodayDraws(activityId, userOpenid);
    }

    /**
     * 查询用户在指定活动的总抽奖次数
     * 
     * @param activityId 活动ID
     * @param userOpenid 用户OpenID
     * @return 抽奖次数
     */
    @Override
    public int countUserTotalDraws(Long activityId, String userOpenid)
    {
        return lotteryRecordMapper.countUserTotalDraws(activityId, userOpenid);
    }

    /**
     * 查询中奖记录列表
     * 
     * @param lotteryRecord 记录信息
     * @return 记录集合
     */
    @Override
    public List<LotteryRecord> selectWinningRecords(LotteryRecord lotteryRecord)
    {
        return lotteryRecordMapper.selectWinningRecords(lotteryRecord);
    }

    /**
     * 查询未领取的中奖记录列表
     * 
     * @param lotteryRecord 记录信息
     * @return 记录集合
     */
    @Override
    public List<LotteryRecord> selectUnclaimedWinningRecords(LotteryRecord lotteryRecord)
    {
        return lotteryRecordMapper.selectUnclaimedWinningRecords(lotteryRecord);
    }

    /**
     * 新增抽奖记录
     * 
     * @param lotteryRecord 记录信息
     * @return 结果
     */
    @Override
    public int insertLotteryRecord(LotteryRecord lotteryRecord)
    {
        lotteryRecord.setCreateTime(DateUtils.getNowDate());
        return lotteryRecordMapper.insertLotteryRecord(lotteryRecord);
    }

    /**
     * 修改抽奖记录
     * 
     * @param lotteryRecord 记录信息
     * @return 结果
     */
    @Override
    public int updateLotteryRecord(LotteryRecord lotteryRecord)
    {
        return lotteryRecordMapper.updateLotteryRecord(lotteryRecord);
    }

    /**
     * 批量删除抽奖记录
     * 
     * @param recordIds 需要删除的记录ID
     * @return 结果
     */
    @Override
    public int deleteLotteryRecordByIds(Long[] recordIds)
    {
        return lotteryRecordMapper.deleteLotteryRecordByIds(recordIds);
    }

    /**
     * 删除抽奖记录信息
     * 
     * @param recordId 记录ID
     * @return 结果
     */
    @Override
    public int deleteLotteryRecordById(Long recordId)
    {
        return lotteryRecordMapper.deleteLotteryRecordById(recordId);
    }

    /**
     * 根据活动ID删除抽奖记录
     * 
     * @param activityId 活动ID
     * @return 结果
     */
    @Override
    public int deleteLotteryRecordByActivityId(Long activityId)
    {
        return lotteryRecordMapper.deleteLotteryRecordByActivityId(activityId);
    }

    /**
     * 根据商家ID删除抽奖记录
     * 
     * @param merchantId 商家ID
     * @return 结果
     */
    @Override
    public int deleteLotteryRecordByMerchantId(Long merchantId)
    {
        return lotteryRecordMapper.deleteLotteryRecordByMerchantId(merchantId);
    }

    /**
     * 执行抽奖
     * 
     * @param activityId 活动ID
     * @param userOpenid 用户OpenID
     * @param userNickname 用户昵称
     * @param userAvatar 用户头像
     * @param tableId 桌台ID
     * @param drawIp 抽奖IP
     * @return 抽奖结果
     */
    @Override
    @Transactional
    public LotteryRecord performDraw(Long activityId, String userOpenid, String userNickname, 
                                   String userAvatar, Long tableId, String drawIp)
    {
        // 检查用户是否可以参与抽奖
        if (!lotteryActivityService.canUserParticipate(activityId, userOpenid))
        {
            throw new ServiceException("您已经参与过此活动的抽奖或活动已结束");
        }
        
        // 获取活动信息
        LotteryActivity activity = lotteryActivityMapper.selectLotteryActivityById(activityId);
        if (activity == null)
        {
            throw new ServiceException("活动不存在");
        }

        // 获取商家信息
        Merchant merchant = merchantService.selectMerchantById(activity.getMerchantId());

        // 创建抽奖记录
        LotteryRecord record = new LotteryRecord();
        record.setActivityId(activityId);
        record.setMerchantId(activity.getMerchantId());
        record.setMerchantCode(merchant != null ? merchant.getMerchantCode() : null);
        record.setTableId(tableId);
        record.setUserOpenid(userOpenid);
        record.setUserNickname(userNickname);
        record.setUserAvatar(userAvatar);
        record.setDrawTime(new Date());
        record.setDrawIp(drawIp);
        record.setIsWinner("0"); // 默认未中奖
        record.setClaimStatus("0"); // 默认未领取
        
        // 执行抽奖逻辑
        JSONObject prizeResult = drawPrize(activity.getPrizeConfig());
        if (prizeResult != null)
        {
            String prizeType = prizeResult.getString("prizeType");
            String prizeName = prizeResult.getString("prizeName");

            // 检查是否是"谢谢惠顾"类型的奖品
            if ("thanks".equals(prizeType) || "谢谢惠顾".equals(prizeName) || "谢谢参与".equals(prizeName))
            {
                // 谢谢惠顾类型，设置为未中奖
                record.setIsWinner("0");
                record.setPrizeName("谢谢惠顾");
                record.setPrizeType("thanks");
                record.setPrizeValue(null);
            }
            else
            {
                // 正常中奖
                record.setIsWinner("1");
                record.setPrizeName(prizeName);
                record.setPrizeType(prizeType);
                record.setPrizeValue(prizeResult.getString("prizeValue"));
            }
        }
        else
        {
            record.setPrizeName("谢谢参与");
            record.setPrizeType("none");
        }
        
        // 保存记录
        insertLotteryRecord(record);
        
        return record;
    }

    /**
     * 抽奖逻辑
     * 
     * @param prizeConfigJson 奖品配置JSON
     * @return 中奖结果
     */
    private JSONObject drawPrize(String prizeConfigJson)
    {
        if (StringUtils.isEmpty(prizeConfigJson))
        {
            return null;
        }
        
        try
        {
            JSONArray prizeConfig = JSON.parseArray(prizeConfigJson);
            if (prizeConfig == null || prizeConfig.isEmpty())
            {
                return null;
            }
            
            // 计算总概率
            double totalProbability = 0.0;
            for (int i = 0; i < prizeConfig.size(); i++)
            {
                JSONObject prize = prizeConfig.getJSONObject(i);
                totalProbability += prize.getDoubleValue("probability");
            }
            
            // 生成随机数
            Random random = new Random();
            double randomValue = random.nextDouble() * totalProbability;
            
            // 确定中奖奖品
            double currentProbability = 0.0;
            for (int i = 0; i < prizeConfig.size(); i++)
            {
                JSONObject prize = prizeConfig.getJSONObject(i);
                currentProbability += prize.getDoubleValue("probability");
                if (randomValue <= currentProbability)
                {
                    return prize;
                }
            }
        }
        catch (Exception e)
        {
            // 解析失败，返回null（未中奖）
        }
        
        return null;
    }

    /**
     * 领取奖品
     * 
     * @param recordId 记录ID
     * @return 结果
     */
    @Override
    public int claimPrize(Long recordId)
    {
        LotteryRecord record = new LotteryRecord();
        record.setRecordId(recordId);
        record.setClaimStatus("1");
        record.setClaimTime(new Date());
        return updateLotteryRecord(record);
    }

    /**
     * 统计抽奖记录数据
     * 
     * @param params 查询参数
     * @return 统计结果
     */
    @Override
    public Map<String, Object> selectLotteryStatistics(Map<String, Object> params)
    {
        return lotteryRecordMapper.selectLotteryStatistics(params);
    }

    /**
     * 统计活动的抽奖数据
     * 
     * @param activityId 活动ID
     * @return 统计结果
     */
    @Override
    public Map<String, Object> selectActivityStatistics(Long activityId)
    {
        return lotteryRecordMapper.selectActivityStatistics(activityId);
    }

    /**
     * 统计商家的抽奖数据
     * 
     * @param merchantId 商家ID
     * @return 统计结果
     */
    @Override
    public Map<String, Object> selectMerchantStatistics(Long merchantId)
    {
        return lotteryRecordMapper.selectMerchantStatistics(merchantId);
    }
}
