@echo off
chcp 65001 >nul
echo ==========================================
echo 商家过期状态自动更新功能安装脚本
echo ==========================================

REM 默认配置
set DEFAULT_DB_NAME=prize_draw_order
set DEFAULT_DB_USER=root

REM 获取参数
if "%1"=="" (
    set DB_NAME=%DEFAULT_DB_NAME%
) else (
    set DB_NAME=%1
)

if "%2"=="" (
    set DB_USER=%DEFAULT_DB_USER%
) else (
    set DB_USER=%2
)

echo 数据库名称: %DB_NAME%
echo 数据库用户: %DB_USER%
echo.

REM 检查SQL文件是否存在
if not exist "sql\merchant_expire_job.sql" (
    echo 错误: 找不到 sql\merchant_expire_job.sql 文件
    echo 请确保在项目根目录下运行此脚本
    pause
    exit /b 1
)

echo 1. 安装定时任务配置...
mysql -u %DB_USER% -p %DB_NAME% < sql\merchant_expire_job.sql

if %errorlevel% equ 0 (
    echo ✓ 定时任务配置安装成功
) else (
    echo ✗ 定时任务配置安装失败
    pause
    exit /b 1
)

echo.
echo 2. 验证安装结果...
mysql -u %DB_USER% -p %DB_NAME% < sql\verify_scheduled_jobs.sql

echo.
echo ==========================================
echo 安装完成！
echo ==========================================
echo.
echo 接下来的步骤：
echo 1. 启动 RuoYi 应用
echo 2. 访问管理后台: http://localhost:18080
echo 3. 导航到: 系统监控 -^> 定时任务
echo 4. 查看并管理商家过期检查任务
echo.
echo 定时任务说明：
echo - 商家过期状态检查: 每分钟执行一次
echo - 商家即将到期提醒: 每天上午9点执行一次
echo.
echo 如需测试功能，请运行:
echo mysql -u %DB_USER% -p %DB_NAME% ^< sql\test_merchant_expire.sql
echo.
echo 详细说明请查看: 商家过期状态自动更新功能说明.md
echo ==========================================
pause
