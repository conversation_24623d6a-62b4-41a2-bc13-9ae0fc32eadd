# 商家过期状态自动更新功能说明

## 功能概述

本功能实现了商家信息在到期后自动更新状态为过期的定时任务，使用 RuoYi 框架封装好的定时任务模块，每分钟执行一次检查。

## 功能特性

1. **自动过期检查**：每分钟执行一次，检查所有已过期的商家并自动更新状态为过期（状态码：2）
2. **即将到期提醒**：每天上午 9 点执行一次，检查即将在 7 天内到期的商家并记录日志提醒
3. **日志记录**：详细的执行日志，包括执行时间、处理数量、错误信息等
4. **安全可靠**：使用数据库事务确保数据一致性

## 技术实现

### 1. 数据库表结构

商家表（merchant）包含以下关键字段：

- `expire_time`：到期时间
- `status`：状态（0=正常，1=停用，2=过期）
- `del_flag`：删除标志（0=存在，2=删除）

### 2. 核心组件

#### MerchantTask 定时任务类

位置：`ruoyi-quartz/src/main/java/com/ruoyi/quartz/task/MerchantTask.java`

主要方法：

- `checkExpiredMerchants()`：检查并更新过期商家状态
- `checkExpiringSoonMerchants()`：检查即将到期的商家

#### MerchantService 服务层

位置：`ruoyi-system/src/main/java/com/ruoyi/system/service/impl/MerchantServiceImpl.java`

主要方法：

- `updateExpiredMerchantsStatus()`：批量更新过期商家状态
- `selectExpiredMerchants()`：查询已过期的商家列表
- `selectExpiringSoonMerchants(int days)`：查询即将到期的商家列表

#### MerchantMapper 数据访问层

位置：`ruoyi-system/src/main/resources/mapper/system/MerchantMapper.xml`

主要 SQL：

```xml
<!-- 查询已过期的商家 -->
<select id="selectExpiredMerchants" resultMap="MerchantResult">
    select * from merchant
    where status != '2' and del_flag = '0'
    and expire_time is not null
    and expire_time < now()
</select>

<!-- 批量更新过期商家状态 -->
<update id="updateExpiredMerchantsStatus">
    update merchant set status = '2', update_time = sysdate()
    where status != '2' and del_flag = '0'
    and expire_time is not null
    and expire_time < now()
</update>
```

## 安装配置

### 1. 执行 SQL 脚本

运行以下 SQL 脚本添加定时任务配置：

```bash
# 进入项目目录
cd prize-draw-order-ruoyi

# 执行SQL脚本（需要根据实际数据库配置调整连接参数）
mysql -u root -p prize_draw_order < sql/merchant_expire_job.sql
```

或者直接在数据库管理工具中执行 `sql/merchant_expire_job.sql` 文件。

### 2. 启动应用

启动 RuoYi 应用后，定时任务会自动加载并开始执行。

### 3. 验证安装

执行验证脚本检查定时任务是否正确配置：

```bash
# 验证定时任务配置
mysql -u root -p prize_draw_order < sql/verify_scheduled_jobs.sql
```

### 4. 功能测试

如需测试功能，可以执行测试脚本：

```bash
# 创建测试数据并验证功能
mysql -u root -p prize_draw_order < sql/test_merchant_expire.sql
```

### 5. 管理定时任务

可以通过 RuoYi 管理后台的"系统监控" -> "定时任务"菜单来管理这些定时任务：

1. 查看任务执行状态
2. 手动执行任务
3. 暂停/恢复任务
4. 查看执行日志

### 6. 访问管理界面

1. 启动应用后访问：http://localhost:18080
2. 使用管理员账号登录
3. 导航到：系统监控 -> 定时任务
4. 查找"商家过期状态检查"和"商家即将到期提醒"任务

## 定时任务配置详情

### 商家过期状态检查任务

- **任务名称**：商家过期状态检查
- **任务组**：SYSTEM
- **调用目标**：merchantTask.checkExpiredMerchants
- **Cron 表达式**：`0 * * * * ?`（每分钟执行一次）
- **状态**：启用

### 商家即将到期提醒任务

- **任务名称**：商家即将到期提醒
- **任务组**：SYSTEM
- **调用目标**：merchantTask.checkExpiringSoonMerchants
- **Cron 表达式**：`0 0 9 * * ?`（每天上午 9 点执行一次）
- **状态**：启用

## 日志监控

### 查看执行日志

1. 应用日志：查看应用控制台输出或日志文件
2. 定时任务日志：通过管理后台的"定时任务日志"查看详细执行记录

### 日志示例

```
2025-08-01 10:30:00 INFO  - 开始执行商家过期状态检查任务
2025-08-01 10:30:00 INFO  - 商家过期状态检查任务完成，共更新了 3 个过期商家的状态
2025-08-01 09:00:00 INFO  - 开始执行商家即将到期检查任务，提前 7 天
2025-08-01 09:00:00 WARN  - 发现 2 个商家即将在 7 天内到期
2025-08-01 09:00:00 WARN  - 商家 [MERCHANT001] 测试商家1 将于 2025-08-05 23:59:59 到期
```

## 注意事项

1. **数据库时间**：确保数据库服务器时间准确，因为过期判断基于数据库的当前时间
2. **性能考虑**：如果商家数量很大，建议调整执行频率或添加分页处理
3. **事务安全**：更新操作使用数据库事务，确保数据一致性
4. **监控告警**：建议配置监控告警，及时发现任务执行异常

## 扩展功能

可以根据业务需要扩展以下功能：

1. **邮件通知**：在商家即将到期时发送邮件通知
2. **短信提醒**：发送短信提醒商家续费
3. **自动续费**：集成支付系统实现自动续费
4. **分级提醒**：不同时间点的多级提醒（如 30 天、7 天、1 天）

## 故障排除

### 常见问题

1. **任务不执行**：检查任务状态是否为启用，检查 Cron 表达式是否正确
2. **执行失败**：查看任务日志，检查数据库连接和权限
3. **更新数量为 0**：检查商家数据的 expire_time 字段是否正确设置

### 调试方法

1. 手动执行任务测试功能
2. 查看详细的执行日志
3. 直接调用 Service 方法进行测试
